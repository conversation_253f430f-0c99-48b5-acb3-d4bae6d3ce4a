import { Component, OnInit } from '@angular/core';
import { User } from '../../../../../models/user.model';
import { Team } from '../../../../../models/team.model';
import { TeamService } from '../../../../../services/team.service';
import { MessageService } from 'primeng/api';
import { Table } from 'primeng/table';
import { UserService } from '../../../../service/user.service';
import { ActivatedRoute } from '@angular/router';

@Component({
    templateUrl: './employees.component.html',
    providers: [MessageService]
})
export class EmployeesComponent implements OnInit {
    userDialog: boolean = false;
    deleteUserDialog: boolean = false;
    deleteUsersDialog: boolean = false;
    users: User[] = [];
    user: User = {} as User;
    selectedUsers: User[] = [];
    submitted: boolean = false;
    cols: any[] = [];
    rowsPerPageOptions = [5, 10, 20];
    teams: Team[] = [];
    constructor(private userService: UserService, private messageService: MessageService, private teamService: TeamService) { }
    ngOnInit() {
        this.userService.getUsers().then(data => {
            this.users = data.filter(u => u.role === 'employe');
        });
        this.teamService.getTeams().subscribe(teams => {
            this.teams = teams;
        });
        this.cols = [
            { field: 'id', header: 'ID' },
            { field: 'name', header: 'Name' },
            { field: 'email', header: 'Email' },
            { field: 'team', header: 'Team' }
        ];
    }
    openNew() {
        this.user = { role: 'employe' } as User;
        this.submitted = false;
        this.userDialog = true;
    }
    // Table global filter
    onGlobalFilter(table: Table, event: Event) {
        const input = event.target as HTMLInputElement;
        table.filterGlobal(input.value, 'contains');
    }

    // CRUD methods
    editUser(user: User) {
        this.user = { ...user };
        this.userDialog = true;
    }

    deleteUser(user: User) {
        this.user = user;
        this.deleteUserDialog = true;
    }

    deleteSelectedUsers() {
        this.deleteUsersDialog = true;
    }

    hideDialog() {
        this.userDialog = false;
        this.submitted = false;
    }

    saveUser() {
        this.submitted = true;
        if (this.user.name && this.user.email) {
            if (this.user.id) {
                // Edit
                this.users[this.findIndexById(this.user.id)] = this.user;
                if (this.user.team_id) {
                    this.userService.updateUserTeam(this.user.id, this.user.team_id).subscribe();
                }
                this.messageService.add({severity:'success', summary: 'Successful', detail: 'Employee Updated', life: 3000});
            } else {
                // Add
                this.user.id = this.createId();
                this.users.push(this.user);
                if (this.user.team_id) {
                    this.userService.updateUserTeam(this.user.id, this.user.team_id).subscribe();
                }
                this.messageService.add({severity:'success', summary: 'Successful', detail: 'Employee Created', life: 3000});
            }
            this.userDialog = false;
            this.user = {} as User;
        }
    }

    confirmDelete() {
        this.users = this.users.filter(val => val.id !== this.user.id);
        this.deleteUserDialog = false;
        this.user = {} as User;
        this.messageService.add({severity:'success', summary: 'Successful', detail: 'Employee Deleted', life: 3000});
    }

    confirmDeleteSelected() {
        this.users = this.users.filter(val => !this.selectedUsers.includes(val));
        this.deleteUsersDialog = false;
        this.selectedUsers = [];
        this.messageService.add({severity:'success', summary: 'Successful', detail: 'Employees Deleted', life: 3000});
    }

    findIndexById(id: number): number {
        return this.users.findIndex(u => u.id === id);
    }

    createId(): number {
        return Math.floor(Math.random() * 10000);
    }
}
