import { Component, OnInit } from '@angular/core';
import { User } from '../../../../api/user';
import { MessageService } from 'primeng/api';
import { Table } from 'primeng/table';
import { UserService } from '../../../../service/user.service';
import { ActivatedRoute } from '@angular/router';

@Component({
    templateUrl: './trainers.component.html',
    providers: [MessageService]
})
export class TrainersComponent implements OnInit {
    userDialog: boolean = false;
    deleteUserDialog: boolean = false;
    deleteUsersDialog: boolean = false;
    users: User[] = [];
    user: User = {} as User;
    selectedUsers: User[] = [];
    submitted: boolean = false;
    cols: any[] = [];
    rowsPerPageOptions = [5, 10, 20];
    constructor(private userService: UserService, private messageService: MessageService) { }
    ngOnInit() {
        this.userService.getUsers().then(data => {
            this.users = data
                .filter(u => u.role === 'formateur')
                .map(u => ({
                    ...u,
                    team: typeof u.team === 'string' || u.team === undefined ? u.team : (u.team?.name ?? '')
                }));
        });
        this.cols = [
            { field: 'id', header: 'ID' },
            { field: 'name', header: 'Name' },
            { field: 'email', header: 'Email' },
            { field: 'specialite', header: 'Spécialité' }
        ];
    }
    openNew() {
        this.user = { role: 'formateur' } as User;
        this.submitted = false;
        this.userDialog = true;
    }
    // Table global filter
    onGlobalFilter(table: Table, event: Event) {
        const input = event.target as HTMLInputElement;
        table.filterGlobal(input.value, 'contains');
    }

    // CRUD methods
    editUser(user: User) {
        this.user = { ...user };
        this.userDialog = true;
    }

    deleteUser(user: User) {
        this.user = user;
        this.deleteUserDialog = true;
    }

    deleteSelectedUsers() {
        this.deleteUsersDialog = true;
    }

    hideDialog() {
        this.userDialog = false;
        this.submitted = false;
    }

    saveUser() {
        this.submitted = true;
        if (this.user.name && this.user.email) {
            if (this.user.id) {
                // Edit
                this.users[this.findIndexById(this.user.id)] = this.user;
                this.messageService.add({severity:'success', summary: 'Successful', detail: 'Trainer Updated', life: 3000});
            } else {
                // Add
                this.user.id = this.createId();
                this.users.push(this.user);
                this.messageService.add({severity:'success', summary: 'Successful', detail: 'Trainer Created', life: 3000});
            }
            this.userDialog = false;
            this.user = {} as User;
        }
    }

    confirmDelete() {
        this.users = this.users.filter(val => val.id !== this.user.id);
        this.deleteUserDialog = false;
        this.user = {} as User;
        this.messageService.add({severity:'success', summary: 'Successful', detail: 'Trainer Deleted', life: 3000});
    }

    confirmDeleteSelected() {
        this.users = this.users.filter(val => !this.selectedUsers.includes(val));
        this.deleteUsersDialog = false;
        this.selectedUsers = [];
        this.messageService.add({severity:'success', summary: 'Successful', detail: 'Trainers Deleted', life: 3000});
    }

    findIndexById(id: number): number {
        return this.users.findIndex(u => u.id === id);
    }

    createId(): number {
        return Math.floor(Math.random() * 10000);
    }
}
