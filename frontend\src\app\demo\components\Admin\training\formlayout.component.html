

<div class="grid flex justify-content-center align-items-center min-h-screen">
  <div class="col-12 md:col-8 lg:col-6">
	<div class="card p-fluid shadow-3 border-round-xl" style="background: #f8f9fa;">
	  <h2 class="text-center mb-4" style="color: #2c3e50;">Add New Training</h2>
	  <form (ngSubmit)="submitFormation()" class="p-4">
		<div class="formgrid grid">
		  <div class="field col-12 md:col-6">
			<label for="name" class="font-bold">Name</label>
			<input pInputText id="name" type="text" [(ngModel)]="formation.name" name="name" placeholder="Enter training name" required />
		  </div>
		  <div class="field col-12 md:col-6">
			<label for="date" class="font-bold">Date</label>
			<input pInputText id="date" type="date" [(ngModel)]="formation.date" name="date" required />
		  </div>
		</div>
		<div class="formgrid grid">
		  <div class="field col-12 md:col-6">
			<label for="duration" class="font-bold">Duration</label>
			<input pInputText id="duration" type="text" [(ngModel)]="formation.duration" name="duration" placeholder="Enter duration (e.g. 2h)" required />
		  </div>
		  <div class="field col-12 md:col-6">
			<label for="team" class="font-bold">Team</label>
			<input pInputText id="team" type="text" [(ngModel)]="formation.team" name="team" placeholder="Enter team name or ID" required />
		  </div>
		</div>
		<div class="field">
		  <label for="trainer" class="font-bold">Trainer</label>
		  <input pInputText id="trainer" type="text" [(ngModel)]="formation.trainer" name="trainer" placeholder="Enter trainer name or ID" required />
		</div>
	<div class="field">
	  <label for="salle" class="font-bold">Room</label>
	  <input pInputText id="salle" type="text" [(ngModel)]="formation.salle" name="salle" placeholder="Enter room" required />
	</div>
	<div class="field">
	  <label for="description" class="font-bold">Description</label>
	  <textarea pInputTextarea id="description" [(ngModel)]="formation.description" name="description" rows="3" placeholder="Enter description" required></textarea>
	</div>
	<div class="flex justify-content-center mt-4">
	  <button pButton type="submit" label="Add Training" class="p-button-success px-5 py-2 font-bold border-round-xl"></button>
	</div>
	  </form>
	</div>
  </div>
</div>
