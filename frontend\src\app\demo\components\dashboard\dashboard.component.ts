import { Component, OnInit, OnDestroy } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { Product } from '../../api/product';
import { ProductService } from '../../service/product.service';
import { StatisticsService, DashboardStats, MonthlyFormations } from '../../../services/statistics.service';
import { Subscription, lastValueFrom } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';

@Component({
    templateUrl: './dashboard.component.html',
})
export class DashboardComponent implements OnInit, OnDestroy {

    items!: MenuItem[];

    products!: Product[];

    chartData: any;

    chartOptions: any;

    subscription!: Subscription;

    // Statistics data
    dashboardStats: DashboardStats | null = null;
    monthlyFormations: MonthlyFormations[] = [];
    loading: boolean = true;

    constructor(
        private productService: ProductService,
        private statisticsService: StatisticsService,
        public layoutService: LayoutService
    ) {
        this.subscription = this.layoutService.configUpdate$.subscribe(() => {
            this.initChart();
        });
    }

    async ngOnInit() {
        this.initChart();
        this.productService.getProductsSmall().then(data => this.products = data);

        this.items = [
            { label: 'Add New', icon: 'pi pi-fw pi-plus' },
            { label: 'Remove', icon: 'pi pi-fw pi-minus' }
        ];

        // Load statistics data
        await this.loadDashboardData();
    }

    async loadDashboardData() {
        try {
            this.loading = true;

            // Load dashboard stats
            this.dashboardStats = await lastValueFrom(this.statisticsService.getDashboardStats());

            // Load monthly formations data
            this.monthlyFormations = await lastValueFrom(this.statisticsService.getMonthlyFormations());

            // Update chart with real data
            this.updateFormationsChart();

        } catch (error) {
            console.error('Error loading dashboard data:', error);
        } finally {
            this.loading = false;
        }
    }

    initChart() {
        const documentStyle = getComputedStyle(document.documentElement);
        const textColor = documentStyle.getPropertyValue('--text-color');
        const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
        const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

        this.chartData = {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
            datasets: [
                {
                    label: 'Formations',
                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data
                    fill: false,
                    backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                    borderColor: documentStyle.getPropertyValue('--primary-500'),
                    tension: .4
                },
                {
                    label: 'Attendance Rate (%)',
                    data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // Will be updated with real data
                    fill: false,
                    backgroundColor: documentStyle.getPropertyValue('--green-600'),
                    borderColor: documentStyle.getPropertyValue('--green-600'),
                    tension: .4,
                    yAxisID: 'y1'
                }
            ]
        };

        this.chartOptions = {
            plugins: {
                legend: {
                    labels: {
                        color: textColor
                    }
                }
            },
            scales: {
                x: {
                    ticks: {
                        color: textColorSecondary
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    ticks: {
                        color: textColorSecondary
                    },
                    grid: {
                        color: surfaceBorder,
                        drawBorder: false
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    ticks: {
                        color: textColorSecondary,
                        max: 100
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        };
    }

    updateFormationsChart() {
        if (!this.monthlyFormations.length) return;

        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const formationsData = new Array(12).fill(0);
        const attendanceData = new Array(12).fill(0);

        this.monthlyFormations.forEach(item => {
            const monthIndex = new Date(item.month + ' 1, 2025').getMonth();
            formationsData[monthIndex] = item.count;
            attendanceData[monthIndex] = item.attendanceRate;
        });

        this.chartData = {
            ...this.chartData,
            datasets: [
                {
                    ...this.chartData.datasets[0],
                    data: formationsData
                },
                {
                    ...this.chartData.datasets[1],
                    data: attendanceData
                }
            ]
        };
    }

    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
