import { Component } from '@angular/core';

@Component({
    templateUrl: './formlayout.component.html'
})
export class FormLayoutComponent {

    formation = {
        name: '',
        description: '',
        date: '',
        duration: '',
        team: '',
        trainer: '',
        salle: ''
    };

    constructor(/* inject your FormationService here if needed */) {}

    submitFormation() {
        // Call your service to POST formation to backend
        // Example: this.formationService.addFormation(this.formation).subscribe(...)
        console.log('Submitting formation:', this.formation);
    }
}
