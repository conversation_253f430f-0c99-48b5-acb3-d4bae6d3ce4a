import { Injectable } from '@angular/core';
import { User } from '../../models/user.model';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class UserService {
   updateUserTeam(userId: number, teamId: number): Observable<any> {
        throw new Error('Method not implemented.');
    }
    // Replace with real API calls
    private users: User[] = [
        { id: 1, name: 'Admin', email: '<EMAIL>', role: 'admin' },
        { id: 2, name: 'Formateur', email: '<EMAIL>', role: 'formateur' },
        { id: 3, name: 'Employé', email: '<EMAIL>', role: 'employe' }
    ];

    getUsers(): Promise<User[]> {
        // Simulate async API
        return Promise.resolve(this.users);
    }

    // Add more CRUD methods as needed
}
