<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Formation;

class FormationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return Formation::with(['equipe', 'formateur'])->get();
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'date' => 'required|date',
            'duree' => 'required|integer',
            'equipe_id' => 'required|exists:teams,id',
            'formateur_id' => 'required|exists:users,id',
            'room' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:active,completed,cancelled,pending',
        ]);
        $formation = Formation::create($validated);
        return response()->json($formation, 201);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $formation = Formation::with(['equipe', 'formateur'])->findOrFail($id);
        return response()->json($formation);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $formation = Formation::findOrFail($id);
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'date' => 'sometimes|required|date',
            'duree' => 'sometimes|required|integer',
            'equipe_id' => 'sometimes|required|exists:teams,id',
            'formateur_id' => 'sometimes|required|exists:users,id',
            'room' => 'nullable|string|max:255',
            'status' => 'nullable|string|in:active,completed,cancelled,pending',
        ]);
        $formation->update($validated);
        return response()->json($formation);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $formation = Formation::findOrFail($id);
        $formation->delete();
        return response()->json(['message' => 'Formation supprimée']);
    }
}
